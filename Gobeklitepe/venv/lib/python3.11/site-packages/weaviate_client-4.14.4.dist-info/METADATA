Metadata-Version: 2.4
Name: weaviate-client
Version: 4.14.4
Summary: A python native Weaviate client
Home-page: https://github.com/weaviate/weaviate-python-client
Author: Weaviate
Author-email: <EMAIL>,
License: BSD 3-clause
Project-URL: Documentation, https://weaviate-python-client.readthedocs.io
Project-URL: Source, https://github.com/weaviate/weaviate-python-client
Project-URL: Tracker, https://github.com/weaviate/weaviate-python-client/issues
Requires-Python: >=3.9
Description-Content-Type: text/x-rst; charset=UTF-8
License-File: LICENSE
Requires-Dist: httpx<0.29.0,>=0.26.0
Requires-Dist: validators==0.34.0
Requires-Dist: authlib<1.3.2,>=1.2.1
Requires-Dist: pydantic<3.0.0,>=2.8.0
Requires-Dist: grpcio<2.0.0,>=1.66.2
Requires-Dist: grpcio-tools<2.0.0,>=1.66.2
Requires-Dist: grpcio-health-checking<2.0.0,>=1.66.2
Requires-Dist: deprecation<3.0.0,>=2.1.0
Provides-Extra: agents
Requires-Dist: weaviate-agents<1.0.0,>=0.3.0; extra == "agents"
Dynamic: license-file

Weaviate python client
======================
.. image:: https://raw.githubusercontent.com/weaviate/weaviate/19de0956c69b66c5552447e84d016f4fe29d12c9/docs/assets/weaviate-logo.png
    :width: 180
    :align: right
    :alt: Weaviate logo

.. image:: https://github.com/weaviate/weaviate-python-client/actions/workflows/.github/workflows/main.yaml/badge.svg?branch=main
    :target: https://github.com/weaviate/weaviate/actions/workflows/.github/workflows/pull_requests.yaml
    :alt: Build Status

.. image:: https://badge.fury.io/py/weaviate-client.svg
    :target: https://badge.fury.io/py/weaviate-client
    :alt: PyPI version

.. image:: https://readthedocs.org/projects/weaviate-python-client/badge/?version=latest
    :target: https://weaviate-python-client.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

A python native client for easy interaction with a Weaviate instance.

The client is tested for python 3.9 and higher.

Visit the official `Weaviate <https://weaviate.io/>`_ website for more information about the Weaviate and how to use it in production.

Client Versions
---------------
We currently support the following versions client versions:

- 4.X: actively supported
- 3.X: deprecated, receives only critical bug fixes and dependency updates
- copy of the 3.X branch in v4 releases: Will be removed on 2024-11-30


Articles
--------

Here are some articles on Weaviate:

- `Semantic Search Queries Return More Informed Results <https://hackernoon.com/semantic-search-queries-return-more-informed-results-nr5335nw>`_
- `Getting Started with Weaviate Python Library <https://towardsdatascience.com/getting-started-with-weaviate-python-client-e85d14f19e4f>`_
- `A sub-50ms neural search with DistilBERT and Weaviate <https://towardsdatascience.com/a-sub-50ms-neural-search-with-distilbert-and-weaviate-4857ae390154>`_

Documentation
-------------

- `Weaviate Python client overview <https://weaviate.io/developers/weaviate/client-libraries/python>`_.
- `Weaviate documentation <https://weaviate.io/developers/weaviate>`_.
- `Additional reference documentation <https://weaviate-python-client.readthedocs.io>`_

Support
-------

- Use our `Forum <https://forum.weaviate.io>`_ for support or any other question.
- Use our `Slack Channel <https://weaviate.io/slack>`_ for discussions or any other question.
- Use the ``weaviate`` tag on `StackOverflow <https://stackoverflow.com/questions/tagged/weaviate>`_  for questions.
- For bugs or problems, submit a GitHub `issue <https://github.com/weaviate/weaviate-python-client/issues>`_.

Contributing
------------
To contribute, read `How to Contribute <https://github.com/weaviate/weaviate-python-client/blob/main/CONTRIBUTING.md>`_.
