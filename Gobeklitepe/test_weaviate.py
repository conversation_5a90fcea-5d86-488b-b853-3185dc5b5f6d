#!/usr/bin/env python3
"""
Test script for Weaviate HVAC CRM setup
Tests basic functionality of Weaviate with text2vec-transformers
"""

import weaviate
import weaviate.classes as wvc
import json
import sys
from typing import Dict, List, Any

def test_weaviate_connection():
    """Test basic connection to Weaviate"""
    try:
        client = weaviate.connect_to_local(
            host="localhost",
            port=8080
        )

        # Test if Weaviate is ready
        if client.is_ready():
            print("✅ Weaviate is ready and responding")
            return client
        else:
            print("❌ Weaviate is not ready")
            return None
    except Exception as e:
        print(f"❌ Failed to connect to Weaviate: {e}")
        return None

def test_meta_info(client):
    """Test retrieving meta information"""
    try:
        meta = client.get_meta()
        print(f"✅ Weaviate version: {meta.version}")
        print(f"✅ Available modules: {list(meta.modules.keys())}")
        return True
    except Exception as e:
        print(f"❌ Failed to get meta info: {e}")
        return False

def create_hvac_schema(client):
    """Create a simple HVAC schema for testing"""
    try:
        # Delete existing collection if it exists
        try:
            client.collections.delete("HVACCustomer")
        except:
            pass

        # Create collection with v4 API
        client.collections.create(
            name="HVACCustomer",
            description="HVAC customer information",
            vectorizer_config=wvc.config.Configure.Vectorizer.text2vec_transformers(),
            properties=[
                wvc.config.Property(
                    name="name",
                    data_type=wvc.config.DataType.TEXT,
                    description="Customer name"
                ),
                wvc.config.Property(
                    name="address",
                    data_type=wvc.config.DataType.TEXT,
                    description="Customer address"
                ),
                wvc.config.Property(
                    name="equipment",
                    data_type=wvc.config.DataType.TEXT,
                    description="HVAC equipment description"
                ),
                wvc.config.Property(
                    name="notes",
                    data_type=wvc.config.DataType.TEXT,
                    description="Service notes and customer information"
                )
            ]
        )

        print("✅ Created HVACCustomer collection")
        return True
    except Exception as e:
        print(f"❌ Failed to create schema: {e}")
        return False

def test_data_insertion(client):
    """Test inserting sample HVAC data"""
    try:
        sample_customers = [
            {
                "name": "Jan Kowalski",
                "address": "ul. Marszałkowska 1, Warszawa",
                "equipment": "LG klimatyzator split 3.5kW",
                "notes": "Instalacja w salonie, wymaga serwisu co 6 miesięcy"
            },
            {
                "name": "Anna Nowak",
                "address": "ul. Nowy Świat 15, Warszawa",
                "equipment": "Daikin pompa ciepła 8kW",
                "notes": "System grzewczy dla całego domu, bardzo zadowolona z wydajności"
            },
            {
                "name": "Piotr Wiśniewski",
                "address": "ul. Krakowskie Przedmieście 5, Warszawa",
                "equipment": "LG VRF system biurowy",
                "notes": "Klimatyzacja dla biura 200m2, problem z jednostką zewnętrzną"
            }
        ]

        # Get the collection
        hvac_collection = client.collections.get("HVACCustomer")

        # Insert data using v4 API
        hvac_collection.data.insert_many(sample_customers)

        print(f"✅ Inserted {len(sample_customers)} sample customers")
        return True
    except Exception as e:
        print(f"❌ Failed to insert data: {e}")
        return False

def test_semantic_search(client):
    """Test semantic search functionality"""
    try:
        # Get the collection
        hvac_collection = client.collections.get("HVACCustomer")

        # Test search for LG equipment
        result = hvac_collection.query.near_text(
            query="LG klimatyzator",
            limit=2,
            return_properties=["name", "equipment", "notes"]
        )

        print(f"✅ Found {len(result.objects)} customers with LG equipment:")
        for obj in result.objects:
            print(f"   - {obj.properties['name']}: {obj.properties['equipment']}")

        # Test search for heating systems
        result = hvac_collection.query.near_text(
            query="pompa ciepła grzewczy",
            limit=2,
            return_properties=["name", "equipment", "address"]
        )

        print(f"✅ Found {len(result.objects)} customers with heating systems:")
        for obj in result.objects:
            print(f"   - {obj.properties['name']}: {obj.properties['equipment']}")

        return True
    except Exception as e:
        print(f"❌ Failed semantic search: {e}")
        return False

def test_aggregation(client):
    """Test aggregation functionality"""
    try:
        # Get the collection
        hvac_collection = client.collections.get("HVACCustomer")

        # Get total count using v4 API
        result = hvac_collection.aggregate.over_all(total_count=True)

        count = result.total_count
        print(f"✅ Total customers in database: {count}")
        return True
    except Exception as e:
        print(f"❌ Failed aggregation test: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Testing Weaviate HVAC CRM Setup")
    print("=" * 50)
    
    # Test connection
    client = test_weaviate_connection()
    if not client:
        sys.exit(1)
    
    # Test meta info
    if not test_meta_info(client):
        sys.exit(1)
    
    # Create schema
    if not create_hvac_schema(client):
        sys.exit(1)
    
    # Test data insertion
    if not test_data_insertion(client):
        sys.exit(1)
    
    # Test semantic search
    if not test_semantic_search(client):
        sys.exit(1)
    
    # Test aggregation
    if not test_aggregation(client):
        sys.exit(1)
    
    print("\n🎉 All tests passed! Weaviate HVAC CRM setup is working correctly.")
    print("\nNext steps:")
    print("- Integrate with GoSpine API")
    print("- Add more complex HVAC schemas")
    print("- Implement customer profile aggregation")
    print("- Set up monitoring with Prometheus/Grafana")

    # Close the client connection
    client.close()

if __name__ == "__main__":
    main()
